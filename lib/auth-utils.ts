'use client';

import { AuthCookies } from './auth-cookies';

export class AuthUtils {
  private static refreshPromise: Promise<boolean> | null = null;

  // Check if token needs refresh (within 5 minutes of expiry)
  static shouldRefreshToken(): boolean {
    const token = AuthCookies.getToken();
    if (!token) return false;

    try {
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      const fiveMinutes = 5 * 60;

      // Refresh if token expires within 5 minutes
      return payload.exp && (payload.exp - currentTime) < fiveMinutes;
    } catch (error) {
      console.error('Error checking token expiry:', error);
      return true; // Assume needs refresh if we can't parse
    }
  }

  // Refresh authentication token
  static async refreshToken(): Promise<boolean> {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;
    
    return result;
  }

  private static async performTokenRefresh(): Promise<boolean> {
    try {
      const currentToken = AuthCookies.getToken();
      if (!currentToken) {
        return false;
      }

      const apiUrl = process.env.NEXT_PUBLIC_API_URL;
      if (!apiUrl) {
        console.error('API URL not configured');
        return false;
      }

      const response = await fetch(`${apiUrl}/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${currentToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      
      if (data.access_token) {
        AuthCookies.setToken(data.access_token);
        
        // Update user data if provided
        if (data.user) {
          AuthCookies.setUser(data.user);
        }
        
        return true;
      }

      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      // Clear invalid tokens
      AuthCookies.clearAll();
      return false;
    }
  }

  // Auto-refresh token if needed
  static async ensureValidToken(): Promise<boolean> {
    const token = AuthCookies.getToken();
    
    if (!token) {
      return false;
    }

    if (AuthCookies.isTokenExpired(token)) {
      AuthCookies.clearAll();
      return false;
    }

    if (this.shouldRefreshToken()) {
      return await this.refreshToken();
    }

    return true;
  }

  // Setup automatic token refresh
  static setupAutoRefresh(): () => void {
    const interval = setInterval(async () => {
      if (this.shouldRefreshToken()) {
        await this.refreshToken();
      }
    }, 60000); // Check every minute

    // Return cleanup function
    return () => clearInterval(interval);
  }

  // Validate session and redirect if needed
  static async validateSession(redirectTo?: string): Promise<boolean> {
    const isValid = await this.ensureValidToken();
    
    if (!isValid && redirectTo) {
      window.location.href = redirectTo;
    }
    
    return isValid;
  }

  // Get user info safely
  static getCurrentUser(): Record<string, unknown> | null {
    try {
      return AuthCookies.getUser();
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Check if user has specific role
  static hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return typeof user?.role === 'string' && user.role === role;
  }

  // Check if user has any of the specified roles
  static hasAnyRole(roles: string[]): boolean {
    const user = this.getCurrentUser();
    return typeof user?.role === 'string' && roles.includes(user.role);
  }

  // Secure logout with server notification
  static async secureLogout(): Promise<void> {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;
      const token = AuthCookies.getToken();
      
      if (apiUrl && token) {
        // Notify server about logout
        await fetch(`${apiUrl}/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Server logout error:', error);
      // Continue with local logout even if server logout fails
    } finally {
      // Always clear local authentication data
      AuthCookies.clearAll();
    }
  }
}
