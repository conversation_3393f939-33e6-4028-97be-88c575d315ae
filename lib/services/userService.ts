import api from '../api';
import { AuthCookies } from '../auth-cookies';

// Define types for user data
export interface User {
  id: number;
  name: string;
  email: string;
  // Add other properties as needed
}

// User login
export const login = async (email: string, password: string) => {
  try {
    const response = await api.post('/login', { email, password });
    // Store token and user data if returned
    if (response.data.token) {
      AuthCookies.setToken(response.data.token);
    }
    if (response.data.user) {
      AuthCookies.setUser(response.data.user);
    }
    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// User registration
export const register = async (userData: {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}) => {
  try {
    const response = await api.post('/register', userData);
    return response.data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

// Get current user profile
export const getCurrentUser = async () => {
  try {
    const response = await api.get('/user');
    return response.data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

// Logout user
export const logout = async () => {
  try {
    const response = await api.post('/logout');
    // Remove all authentication data from secure storage
    AuthCookies.clearAll();
    return response.data;
  } catch (error) {
    console.error('Logout error:', error);
    // Clear local auth data even if server logout fails
    AuthCookies.clearAll();
    throw error;
  }
};