/**
 * Coming Soon Page Configuration
 * 
 * This file controls the coming soon page behavior.
 * Set ENABLE_COMING_SOON to false when ready to go public.
 */

export const COMING_SOON_CONFIG = {
  // Main toggle - set to false to disable coming soon page
  ENABLE_COMING_SOON: true,
  
  // Access methods configuration
  ACCESS_METHODS: {
    // URL parameter: /?preview=true
    URL_PARAM: {
      enabled: true,
      param: 'preview',
      value: 'true'
    },
    
    // Key sequence: type "admin" anywhere on the coming soon page
    KEY_SEQUENCE: {
      enabled: true,
      sequence: 'admin'
    },
    
    // Hidden click area (click logo 5 times)
    CLICK_SEQUENCE: {
      enabled: true,
      clicks: 5,
      timeWindow: 3000 // 3 seconds
    },
    
    // Direct access route: /preview
    DIRECT_ROUTE: {
      enabled: true,
      route: '/preview'
    }
  },
  
  // Storage configuration
  STORAGE: {
    key: 'jemputmakan_access_granted',
    expiry: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  },
  
  // Content configuration
  CONTENT: {
    brandName: '',
    tagline: 'Delicious meals, delivered with care',
    message: 'We are working hard to bring you an amazing meal planning experience. Stay tuned!',
    logo: '/images/jemput_makan_logo.png',
    backgroundImage: '/images/main-meal.jpg'
  }
};

// Helper function to check if coming soon should be shown
export const shouldShowComingSoon = (): boolean => {
  return COMING_SOON_CONFIG.ENABLE_COMING_SOON;
};

// Helper function to get access storage key with expiry
export const getAccessStorageKey = (): string => {
  return COMING_SOON_CONFIG.STORAGE.key;
};

// Helper function to check if access is still valid
export const isAccessValid = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    const stored = localStorage.getItem(COMING_SOON_CONFIG.STORAGE.key);
    if (!stored) return false;
    
    const { timestamp } = JSON.parse(stored);
    const now = Date.now();
    const expiry = COMING_SOON_CONFIG.STORAGE.expiry;
    
    return (now - timestamp) < expiry;
  } catch {
    return false;
  }
};

// Helper function to grant access
export const grantAccess = (): void => {
  if (typeof window === 'undefined') return;
  
  const accessData = {
    granted: true,
    timestamp: Date.now()
  };
  
  localStorage.setItem(
    COMING_SOON_CONFIG.STORAGE.key, 
    JSON.stringify(accessData)
  );
};

// Helper function to revoke access
export const revokeAccess = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(COMING_SOON_CONFIG.STORAGE.key);
};
