import axios from 'axios';
import { AuthCookies } from './auth-cookies';

// Create an axios instance with default config
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
});

// Add a request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    if (typeof window !== 'undefined') {
      const token = AuthCookies.getToken();
      if (token && !AuthCookies.isTokenExpired(token)) {
        config.headers.Authorization = `Bearer ${token}`;
      } else if (token && AuthCookies.isTokenExpired(token)) {
        // Clear expired token
        AuthCookies.clearAll();
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle common errors here
    if (error.response) {
      // Server responded with a status code outside of 2xx range
      const statusCode = error.response.status;
      const errorData = error.response.data;
      
      switch (statusCode) {
        case 401:
          console.error('Authentication Error: You need to login', errorData);
          // Clear authentication data on 401 errors
          if (typeof window !== 'undefined') {
            AuthCookies.clearAll();
          }
          break;
        case 403:
          console.error('Authorization Error: You do not have permission', errorData);
          break;
        case 404:
          console.error('Not Found Error: The requested resource was not found', errorData);
          break;
        case 422:
          console.error('Validation Error: The data provided is invalid', errorData);
          break;
        case 500:
        case 502:
        case 503:
          console.error('Server Error: Something went wrong on the server', errorData);
          break;
        default:
          console.error(`API Error (${statusCode}):`, errorData);
      }
    } else if (error.request) {
      // Request was made but no response was received
      console.error('Network Error: No response received from server. Please check your internet connection.');
    } else {
      // Something else happened while setting up the request
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default api;