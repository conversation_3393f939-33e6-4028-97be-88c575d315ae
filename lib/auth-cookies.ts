'use client';

// Client-side cookie utilities for authentication
// Note: For production, consider using server-side cookies with httpOnly flag

interface CookieOptions {
  expires?: Date;
  maxAge?: number;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  path?: string;
}

export class AuthCookies {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly USER_KEY = 'auth_user';

  // Set authentication token with secure options
  static setToken(token: string, options: CookieOptions = {}): void {
    const defaultOptions: CookieOptions = {
      maxAge: 60 * 60 * 24 * 7, // 7 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
    };

    const finalOptions = { ...defaultOptions, ...options };
    this.setCookie(this.TOKEN_KEY, token, finalOptions);
  }

  // Get authentication token
  static getToken(): string | null {
    return this.getCookie(this.TOKEN_KEY);
  }

  // Set user data (non-sensitive information only)
  static setUser(user: Record<string, unknown>, options: CookieOptions = {}): void {
    const defaultOptions: CookieOptions = {
      maxAge: 60 * 60 * 24 * 7, // 7 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
    };

    const finalOptions = { ...defaultOptions, ...options };
    // Only store non-sensitive user data
    const safeUserData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
    };
    this.setCookie(this.USER_KEY, JSON.stringify(safeUserData), finalOptions);
  }

  // Get user data
  static getUser(): Record<string, unknown> | null {
    const userData = this.getCookie(this.USER_KEY);
    if (!userData) return null;
    
    try {
      return JSON.parse(userData);
    } catch (error) {
      console.error('Error parsing user data from cookie:', error);
      this.clearUser();
      return null;
    }
  }

  // Clear authentication token
  static clearToken(): void {
    this.deleteCookie(this.TOKEN_KEY);
  }

  // Clear user data
  static clearUser(): void {
    this.deleteCookie(this.USER_KEY);
  }

  // Clear all authentication data
  static clearAll(): void {
    this.clearToken();
    this.clearUser();
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Private helper methods
  private static setCookie(name: string, value: string, options: CookieOptions): void {
    if (typeof document === 'undefined') return;

    let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

    if (options.maxAge) {
      cookieString += `; Max-Age=${options.maxAge}`;
    }

    if (options.expires) {
      cookieString += `; Expires=${options.expires.toUTCString()}`;
    }

    if (options.path) {
      cookieString += `; Path=${options.path}`;
    }

    if (options.secure) {
      cookieString += '; Secure';
    }

    if (options.sameSite) {
      cookieString += `; SameSite=${options.sameSite}`;
    }

    document.cookie = cookieString;
  }

  private static getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null;

    const nameEQ = encodeURIComponent(name) + '=';
    const cookies = document.cookie.split(';');

    for (let cookie of cookies) {
      cookie = cookie.trim();
      if (cookie.indexOf(nameEQ) === 0) {
        return decodeURIComponent(cookie.substring(nameEQ.length));
      }
    }

    return null;
  }

  private static deleteCookie(name: string): void {
    if (typeof document === 'undefined') return;

    document.cookie = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
  }

  // Token validation helper
  static isTokenExpired(token: string): boolean {
    if (!token) return true;

    try {
      // Basic JWT structure check
      const parts = token.split('.');
      if (parts.length !== 3) return true;

      // Decode payload (basic check, not cryptographic verification)
      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if token has expiration and if it's expired
      if (payload.exp && payload.exp < currentTime) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error validating token:', error);
      return true;
    }
  }

  // Auto-cleanup expired tokens
  static cleanupExpiredTokens(): void {
    const token = this.getToken();
    if (token && this.isTokenExpired(token)) {
      this.clearAll();
    }
  }
}
