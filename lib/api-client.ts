'use client';

// Enhanced API client with better error handling and debugging
export class ApiClient {
  private static baseUrl: string | null = null;

  // Get the API base URL with validation
  static getBaseUrl(): string {
    if (this.baseUrl) {
      return this.baseUrl;
    }

    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    console.log('Environment API URL:', apiUrl);
    
    if (!apiUrl) {
      console.error('NEXT_PUBLIC_API_URL is not defined in environment variables');
      throw new Error('API URL not configured. Please check your .env.local file.');
    }

    this.baseUrl = apiUrl;
    return apiUrl;
  }

  // Enhanced fetch with better error handling
  static async fetch(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const baseUrl = this.getBaseUrl();
    const url = `${baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    
    console.log('Making API request to:', url);
    console.log('Request options:', options);

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      return response;
    } catch (error) {
      console.error('Fetch error:', error);
      
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Network error: Unable to connect to the server. Please check if your Laravel backend is running and accessible.');
      }
      
      throw error;
    }
  }

  // GET request helper
  static async get(endpoint: string): Promise<unknown> {
    const response = await this.fetch(endpoint, { method: 'GET' });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('API Response data:', data);
    return data;
  }

  // POST request helper
  static async post(endpoint: string, data: Record<string, unknown>): Promise<unknown> {
    const response = await this.fetch(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  }

  // Test API connectivity
  static async testConnection(): Promise<boolean> {
    try {
      const baseUrl = this.getBaseUrl();
      console.log('Testing connection to:', baseUrl);
      
      // Try a simple HEAD request to test connectivity
      const response = await fetch(baseUrl, { 
        method: 'HEAD',
        mode: 'cors',
      });
      
      console.log('Connection test result:', response.status);
      return response.status < 500; // Accept any non-server error
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  // Get detailed error information
  static getErrorDetails(error: unknown): string {
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return 'Network Error: Cannot connect to the server. Possible causes:\n' +
             '1. Laravel backend is not running\n' +
             '2. CORS is not properly configured\n' +
             '3. API URL is incorrect\n' +
             '4. Network connectivity issues';
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    return 'Unknown error occurred';
  }
}
