'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  shouldShowComingSoon, 
  isAccessValid, 
  grantAccess,
  COMING_SOON_CONFIG 
} from '@/lib/config/coming-soon';

interface ComingSoonContextType {
  showComingSoon: boolean;
  grantAccess: () => void;
  checkAccess: () => void;
}

const ComingSoonContext = createContext<ComingSoonContextType | undefined>(undefined);

export const useComingSoon = () => {
  const context = useContext(ComingSoonContext);
  if (context === undefined) {
    throw new Error('useComingSoon must be used within a ComingSoonProvider');
  }
  return context;
};

interface ComingSoonProviderProps {
  children: React.ReactNode;
}

export const ComingSoonProvider: React.FC<ComingSoonProviderProps> = ({ children }) => {
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Check access on mount and URL changes
  const checkAccess = () => {
    if (!isClient) return;

    // If coming soon is disabled globally, don't show it
    if (!shouldShowComingSoon()) {
      setShowComingSoon(false);
      return;
    }

    // Check if access is already granted and valid
    if (isAccessValid()) {
      setShowComingSoon(false);
      return;
    }

    // Check URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const previewParam = urlParams.get(COMING_SOON_CONFIG.ACCESS_METHODS.URL_PARAM.param);
    
    if (COMING_SOON_CONFIG.ACCESS_METHODS.URL_PARAM.enabled && 
        previewParam === COMING_SOON_CONFIG.ACCESS_METHODS.URL_PARAM.value) {
      handleGrantAccess();
      return;
    }

    // Check if we're on the direct access route
    if (COMING_SOON_CONFIG.ACCESS_METHODS.DIRECT_ROUTE.enabled &&
        window.location.pathname === COMING_SOON_CONFIG.ACCESS_METHODS.DIRECT_ROUTE.route) {
      handleGrantAccess();
      // Redirect to home page with access granted
      window.history.replaceState({}, '', '/');
      return;
    }

    // If no access method matched, show coming soon
    setShowComingSoon(true);
  };

  const handleGrantAccess = () => {
    grantAccess();
    setShowComingSoon(false);
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (isClient) {
      checkAccess();
    }
  }, [isClient]);

  // Listen for URL changes (for SPA navigation)
  useEffect(() => {
    if (!isClient) return;

    const handlePopState = () => {
      checkAccess();
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [isClient]);

  const value: ComingSoonContextType = {
    showComingSoon,
    grantAccess: handleGrantAccess,
    checkAccess
  };

  return (
    <ComingSoonContext.Provider value={value}>
      {children}
    </ComingSoonContext.Provider>
  );
};
