'use client';

import React, { createContext, useContext, useState } from 'react';

interface MealPlanState {
  pax?: number;
  subscriptionDuration?: number;
  startDate?: string;
  mealOption?: 'lunch' | 'dinner' | 'lunch&dinner';
  riceIncluded?: boolean;
  diet?: {
    selectedPackage: 'basic' | 'premium' | 'deluxe';
    includes?: {
      protein: number;
      side: number;
      veggies: number;
      dessert: number;
    };
  };
  userDetails?: {
    name: string;
    phone: string;
    address1: string;
    address2?: string;
    postcode: string;
    state: string;
    country: string;
  };
  paymentStatus?: 'completed' | 'failed';
}

interface MealPlanContextType {
  state: MealPlanState;
  setState: (update: Partial<MealPlanState>) => void;
}

const MealPlanContext = createContext<MealPlanContextType | undefined>(undefined);

export const MealPlanProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setStateInternal] = useState<MealPlanState>({});

  const setState = (update: Partial<MealPlanState>) => {
    setStateInternal(prev => ({ ...prev, ...update }));
  };

  return (
    <MealPlanContext.Provider value={{ state, setState }}>
      {children}
    </MealPlanContext.Provider>
  );
};

export const useMealPlan = () => {
  const context = useContext(MealPlanContext);
  if (!context) {
    throw new Error('useMealPlan must be used within a MealPlanProvider');
  }
  return context;
}; 