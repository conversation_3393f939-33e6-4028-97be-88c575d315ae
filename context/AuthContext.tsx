'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthCookies } from '@/lib/auth-cookies';
import { AuthUtils } from '@/lib/auth-utils';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  phone: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ user: User; access_token: string }>;
  logout: () => void;
  loading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();

  // Setup automatic token refresh
  useEffect(() => {
    const cleanup = AuthUtils.setupAutoRefresh();
    return cleanup;
  }, []);

  // Initialize auth state from secure cookies on component mount
  useEffect(() => {
    // Clean up any expired tokens first
    AuthCookies.cleanupExpiredTokens();

    const storedUser = AuthCookies.getUser();
    const storedToken = AuthCookies.getToken();

    if (storedUser && storedToken && !AuthCookies.isTokenExpired(storedToken)) {
      // Type assertion for stored user data
      const typedUser = storedUser as unknown as User;
      setUser(typedUser);
      setToken(storedToken);
    } else if (storedToken && AuthCookies.isTokenExpired(storedToken)) {
      // Clear expired tokens
      AuthCookies.clearAll();
    }

    setIsInitialized(true);
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;
      if (!apiUrl) {
        throw new Error('API URL not configured');
      }

      const response = await fetch(`${apiUrl}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Store user data and token
      setUser(data.user);
      setToken(data.access_token);

      // Save to secure cookies
      AuthCookies.setUser(data.user);
      AuthCookies.setToken(data.access_token);
      
      return data;
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An error occurred during login');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);

    try {
      // Perform secure logout with server notification
      await AuthUtils.secureLogout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear user data and token from state
      setUser(null);
      setToken(null);
      setLoading(false);

      // Redirect to home page
      router.push('/');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        isAuthenticated: !!user,
        login,
        logout,
        loading,
        error,
      }}
    >
      {isInitialized && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};