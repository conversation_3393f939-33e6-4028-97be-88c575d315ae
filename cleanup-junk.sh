#!/bin/bash

# Cleanup script for UUID junk files in Next.js project
# This script safely removes thousands of UUID-named temporary files

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "next.config.ts" ]; then
    print_error "This doesn't appear to be a Next.js project directory!"
    print_error "Please run this script from your project root."
    exit 1
fi

print_status "Starting cleanup of UUID junk files..."

# Count junk files before cleanup
JUNK_COUNT=$(ls -1 | grep -E '^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$' | wc -l)
print_status "Found $JUNK_COUNT UUID junk files/directories to remove"

if [ "$JUNK_COUNT" -eq 0 ]; then
    print_success "No junk files found! Your directory is already clean."
    exit 0
fi

# Ask for confirmation
echo ""
print_warning "This will remove $JUNK_COUNT UUID-named files and directories."
print_warning "These appear to be temporary files that weren't cleaned up properly."
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Cleanup cancelled."
    exit 0
fi

print_status "Creating backup list of files being removed..."
ls -1 | grep -E '^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$' > junk_files_removed.txt

print_status "Removing UUID junk files..."
# Remove all UUID-named files and directories
ls -1 | grep -E '^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$' | xargs rm -rf

# Also clean up some other obvious junk files
print_status "Cleaning up other temporary files..."

# Remove augment temp directories
rm -rf augment-zsh-* 2>/dev/null || true

# Remove Apple service temp directories (these shouldn't be in your project)
rm -rf com.apple.* 2>/dev/null || true

# Remove other temp files
rm -rf exthost-*.cpuprofile 2>/dev/null || true
rm -rf vscode-* 2>/dev/null || true
rm -rf node-compile-cache 2>/dev/null || true
rm -rf node-jiti 2>/dev/null || true
rm -rf xcrun_db 2>/dev/null || true

# Remove build artifacts that shouldn't be in root
rm -rf _next 2>/dev/null || true
rm -f *.html 2>/dev/null || true
rm -f *.txt 2>/dev/null || true

# Remove SVG files that should be in public/
if [ -f "file.svg" ] && [ -f "public/file.svg" ]; then
    rm -f file.svg globe.svg next.svg vercel.svg window.svg 2>/dev/null || true
fi

print_success "Cleanup completed!"

# Show final count
REMAINING_JUNK=$(ls -1 | grep -E '^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$' | wc -l 2>/dev/null || echo "0")
print_status "Remaining UUID files: $REMAINING_JUNK"

print_status "Current directory contents:"
ls -la | head -20

print_success "🎉 Your project directory is now clean!"
print_status "A list of removed files was saved to: junk_files_removed.txt"

echo ""
print_status "To prevent this from happening again:"
print_status "1. Check what processes are creating these temp files"
print_status "2. Make sure your .gitignore excludes temp directories"
print_status "3. Regularly clean up temp files"
