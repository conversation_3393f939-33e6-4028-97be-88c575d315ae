# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/

# production
/build
/out

# misc
.DS_Store
*.pem

# Temporary files and directories
tmp.*
temp.*
*.tmp
*.temp
*-*-*-*-*
augment-zsh-*
com.apple.*
exthost-*.cpuprofile
vscode-*
node-compile-cache
node-jiti
xcrun_db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
node_modules
