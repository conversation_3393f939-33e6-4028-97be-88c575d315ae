{"name": "familimeal-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build"}, "dependencies": {"@headlessui/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.4.2", "lucide-react": "^0.479.0", "next": "15.1.7", "react": "^19.0.0", "react-day-picker": "^9.6.1", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8.5.2", "tailwindcss": "^3.4.17", "typescript": "^5"}}