@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #F9F9F9;
  --foreground: #333333;
  --muted: #F3F4F6;
  --muted-foreground: #6B7280;
  --primary: #2E7D32;
  --primary-hover: #E8F5E9;
  --secondary: #1D159B;
  --secondary-hover: #3A2BC9;
  --accent: #FFB504; 
  --accent-hover: #E09E03;
  --border: #E5E7EB;
  --input: #F9FAFB;
  --ring: var(--primary);
  --radius: 0.75rem;
   /* OLD ACCENT COLOR FFD54F */
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0F172A;
    --foreground: #F8FAFC;
    --muted: #1E293B;
    --muted-foreground: #94A3B8;
    --app-blue: #1D4ED8;
    --app-blue-hover: #2563EB;
    --border: #334155;
    --input: #1E293B;
    --ring: var(--app-blue);
  }
} */

@layer base {
  body {
    @apply bg-background text-foreground antialiased;
  }
  h1 {
    @apply text-4xl font-bold tracking-tight;
  }
  h2 {
    @apply text-2xl font-semibold tracking-tight;
  }
  h3 {
    @apply text-xl font-semibold;
  }
}

@layer utilities {
  .focus\:ring-app-blue:focus {
    --tw-ring-color: var(--app-blue);
    --tw-ring-opacity: 1;
  }
}

@layer components {
  .form-container {
    @apply bg-white p-6 rounded-lg shadow-sm border border-border;
  }

  .input-base {
    @apply w-full px-4 py-3 rounded-lg border border-border bg-background
           text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50;
  }

  .btn-primary {
    @apply px-6 py-3 bg-primary text-white rounded-full font-medium
           hover:bg-primary-hover transition-colors duration-200
           focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-muted text-foreground rounded-full font-medium
           hover:bg-muted-foreground hover:bg-opacity-10 transition-colors duration-200
           focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }
}