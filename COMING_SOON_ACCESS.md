# Coming Soon Page - Access Instructions

## Overview

The JemputMakan application now features a "Coming Soon" landing page that is displayed to all visitors by default. This page showcases the brand while the application is in development/testing phase.

## For Authorized Users (Admin/Partners)

There are **4 different ways** to bypass the coming soon page and access the full application:

### Method 1: URL Parameter
Add `?preview=true` to any URL:
- `https://yoursite.com/?preview=true`
- `https://yoursite.com/auth?preview=true`
- `https://yoursite.com/meal-plan?preview=true`

### Method 2: Direct Access Route
Visit the special preview route:
- `https://yoursite.com/preview`

This will automatically grant access and redirect you to the home page.

### Method 3: Key Sequence
1. Visit the coming soon page
2. Type `admin` anywhere on the page (no input field needed)
3. Access will be granted automatically

### Method 4: Logo Click Sequence
1. Visit the coming soon page
2. Click the JemputMakan logo **5 times quickly** (within 3 seconds)
3. Access will be granted automatically

## Access Persistence

Once access is granted using any method:
- Access is stored in browser localStorage
- Access remains valid for **24 hours**
- You can navigate freely throughout the application
- Access persists across browser sessions (until expiry)

## Revoking Access

When you have access, you'll see a red "Admin Access Active" indicator in the bottom-right corner with a "Revoke Access" button. Click it to return to the coming soon page.

## Development Mode

In development mode (`npm run dev`), the coming soon page shows helpful hints about all access methods.

## Configuration

### Enabling/Disabling Coming Soon Page

Edit `lib/config/coming-soon.ts`:

```typescript
export const COMING_SOON_CONFIG = {
  // Set to false to disable coming soon page entirely
  ENABLE_COMING_SOON: true,
  
  // ... other settings
};
```

### Customizing Access Methods

You can enable/disable individual access methods in the same config file:

```typescript
ACCESS_METHODS: {
  URL_PARAM: { enabled: true, param: 'preview', value: 'true' },
  KEY_SEQUENCE: { enabled: true, sequence: 'admin' },
  CLICK_SEQUENCE: { enabled: true, clicks: 5, timeWindow: 3000 },
  DIRECT_ROUTE: { enabled: true, route: '/preview' }
}
```

### Customizing Content

Update the content section in the config:

```typescript
CONTENT: {
  brandName: 'JemputMakan',
  tagline: 'Delicious meals, delivered with care',
  message: 'We are working hard to bring you an amazing meal planning experience. Stay tuned!',
  logo: '/images/jemput_makan_logo.png',
  backgroundImage: '/images/main-meal.jpg'
}
```

## Going Live

When ready to make the application public:

1. Set `ENABLE_COMING_SOON: false` in `lib/config/coming-soon.ts`
2. Build and deploy the application
3. The coming soon page will no longer appear

## Technical Details

- **Static Export Compatible**: Works with Next.js static export
- **Client-Side Only**: No server-side dependencies
- **Lightweight**: Minimal impact on bundle size
- **Accessible**: Keyboard navigation and screen reader friendly
- **Responsive**: Works on all device sizes

## Security Notes

- Access methods are client-side only (suitable for development/testing)
- For production with real security needs, implement server-side authentication
- The key sequence and click methods are "security through obscurity"
- URL parameters can be shared easily with team members

## Troubleshooting

### Access Not Working
1. Clear browser localStorage: `localStorage.clear()`
2. Try a different access method
3. Check browser console for errors
4. Ensure JavaScript is enabled

### Coming Soon Page Not Showing
1. Check `ENABLE_COMING_SOON` setting in config
2. Clear browser cache
3. Verify build completed successfully

### Access Expired
Access automatically expires after 24 hours. Use any access method again to regain access.
