import '../styles/globals.css';
import { Poppins } from 'next/font/google';
import { MealPlanProvider } from '@/context/MealPlanContext';
import { LanguageProvider } from '@/context/LanguageContext';
import { AuthProvider } from '@/context/AuthContext';
import { ComingSoonProvider } from '@/context/ComingSoonContext';
import Navbar from '@/components/layout/Navbar';
import ComingSoonWrapper from '@/components/ComingSoonWrapper';
import AdminAccess from '@/components/AdminAccess';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
});

export const metadata = {
  title: 'JemputMakan',
  description: 'A meal planning app for families',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning className={`${poppins.variable}`}>
      <body className="bg-background min-h-auto font-poppins">
        <ComingSoonProvider>
          <ComingSoonWrapper>
            <LanguageProvider>
              <AuthProvider>
                <MealPlanProvider>
                  <Navbar />
                  <main className="pt-16"> {/* Add padding top to account for fixed navbar */}
                    {children}
                  </main>
                  <AdminAccess />
                </MealPlanProvider>
              </AuthProvider>
            </LanguageProvider>
          </ComingSoonWrapper>
        </ComingSoonProvider>
      </body>
    </html>
  )
}
