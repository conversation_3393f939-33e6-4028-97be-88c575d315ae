"use client";

import React, { useState, useRef, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { MealPlanProvider } from '@/context/MealPlanContext';
import { ProgressSteps } from '@/components/form/navigation/ProgressSteps';
import MealConfiguration from '@/components/form/steps/MealConfiguration';
import MealPreferences from '@/components/form/steps/MealPreferences';
import ContactInfo from '@/components/form/steps/ContactInfo';
import Summary from '@/components/form/steps/Summary';
import Payment from '@/components/form/steps/elements/PaymentStep';
import ApiDebugger from '@/components/debug/ApiDebugger';


interface StepComponentProps {
  onNext: () => void;
  onBack?: () => void;
}

const steps: { title: string; component: React.ReactElement<StepComponentProps> }[] = [
  { 
    title: 'Configuration', 
    component: <MealConfiguration onNext={() => {}} /> 
  },
  { 
    title: 'Preferences', 
    component: <MealPreferences onNext={() => {}} onBack={() => {}} /> 
  },
  { 
    title: 'Contact', 
    component: <ContactInfo onNext={() => {}} onBack={() => {}} /> 
  },
  { 
    title: 'Review', 
    component: <Summary onNext={() => {}} onBack={() => {}} /> 
  },
  { 
    title: 'Payment', 
    component: <Payment onNext={() => {}} onBack={() => {}} /> 
  }
];

const slideVariants = {
  initial: { x: 50, opacity: 0 },
  animate: { x: 0, opacity: 1 },
  exit: { x: -50, opacity: 0 },
};

const MealPlanPage = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const hasMounted = useRef(false);

  useEffect(() => {
    hasMounted.current = true;
  }, []);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const stepsWithHandlers = steps.map((step) => ({
    ...step,
    component: React.cloneElement(step.component as React.ReactElement<StepComponentProps>, {
      onNext: handleNext,
      onBack: handleBack,
    }),
  }));

  return (
    <MealPlanProvider>
      <div 
        ref={containerRef}
        className="min-h-[100dvh] bg-background overflow-y-auto"
      >
        <div className="max-w-md mx-auto px-4 py-6 safe-area-padding">
          {/* Temporary API Debugger - Remove after testing */}
          <div className="mb-6">
            <ApiDebugger />
          </div>

          <ProgressSteps
            currentStep={currentStep}
            totalSteps={steps.length}
            steps={steps}
          />

          <div className="relative">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                variants={slideVariants}
                initial={hasMounted.current ? "initial" : false}
                animate="animate"
                exit="exit"
                transition={{ duration: 0.4 }}
              >
                {stepsWithHandlers[currentStep].component}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </MealPlanProvider>
  );
};

export default MealPlanPage; 