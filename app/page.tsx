'use client';
import Link from 'next/link';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Hero Section */}
      <header className="relative w-full bg-black text-white overflow-hidden min-h-screen flex items-center pt-0 -mt-16">
        <div className="absolute inset-0 z-0">
          <Image 
            src="/images/main-meal.jpg" 
            alt="Hero Background" 
            fill 
            className="object-cover opacity-50"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/20 to-black/60"></div>
        </div>
        
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-16 flex flex-col items-center text-center w-full">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 text-white drop-shadow-2xl leading-tight">
              Juadah Premium Dihantar Terus Kepada Anda
            </h1>
            
            <p className="text-lg sm:text-xl lg:text-2xl mb-10 max-w-3xl mx-auto text-white/90 leading-relaxed">
              Kami Urus Pemakanan Anda & Keluarga Anda Dengan Penjimatan Masa & Wang
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button asChild className="group relative overflow-hidden rounded-full text-lg px-10 py-4 h-auto text-black bg-accent hover:bg-accent/90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <Link href="/meal-plan" className="flex items-center gap-2">
                  <span>Jom Langgan</span>
                  <svg className="w-5 h-5 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-16 bg-gradient-to-r from-black via-gray-900 to-black text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
            <div className="flex items-center justify-center p-6 lg:p-8">
              <div className="text-center">
                <div className="mb-4 transform hover:scale-105 transition-transform duration-300">
                  <Image 
                    src="/images/jemput_makan_logo.png" 
                    alt="Muslim Kitchen" 
                    width={280} 
                    height={180} 
                    className="mx-auto drop-shadow-lg"
                  />
                </div>
                <h3 className="text-xl lg:text-2xl font-bold text-white">Ekslusif Dari Dapur Muslim</h3>
                <p className="text-white/80 mt-2 text-sm lg:text-base">Makanan halal berkualiti tinggi</p>
              </div>
            </div>
            <div className="flex items-center justify-center p-6 lg:p-8">
              <div className="text-center">
                <div className="mb-4 transform hover:scale-105 transition-transform duration-300">
                  <div className="w-16 h-16 lg:w-20 lg:h-20 mx-auto bg-accent/20 rounded-full flex items-center justify-center mb-2">
                    <svg className="w-8 h-8 lg:w-10 lg:h-10 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl lg:text-2xl font-bold text-white">Penghantaran Makanan</h3>
                <p className="text-white/80 mt-2 text-sm lg:text-base">Terus ke pintu rumah anda</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Subscription Plans Section */}
      <section className="py-20 bg-gradient-to-br from-background via-muted/30 to-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">Pilihan Plan:</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Pilih pelan yang sesuai dengan keperluan dan bajet keluarga anda
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 lg:gap-12 max-w-5xl mx-auto">
            {/* Solo Box */}
            <Card className="group relative overflow-hidden bg-gradient-to-br from-accent/5 to-accent/10 border-2 border-accent/20 hover:border-accent/40 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-2xl">
              <CardHeader className="pb-0">
                <div className="relative h-56 -mx-6 -mt-6 mb-6 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent z-10"></div>
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-20">
                    <div className="bg-accent/90 backdrop-blur-sm rounded-full p-4 transform group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-8 h-8 text-black" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    </div>
                  </div>
                  <Image 
                    src="/images/solo-box.jpg" 
                    alt="Solo Box" 
                    fill 
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <CardTitle className="text-2xl lg:text-3xl font-bold text-center">Economy Class</CardTitle>
              </CardHeader>
              <CardContent className="pt-4 text-center">
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Menu ringkas dan berpatutan untuk makan pantas. Sesuai untuk individu atau pasangan.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-center gap-2">
                    <svg className="w-4 h-4 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>1-2 Pax</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <svg className="w-4 h-4 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Menu Harian</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-6">
                <Button asChild className="w-full bg-accent hover:bg-accent/90 text-black font-semibold py-3 rounded-full transition-all duration-300 transform hover:scale-105">
                  <Link href="/meal-plan">Tempah Sekarang</Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Family Meal */}
            <Card className="group relative overflow-hidden bg-gradient-to-br from-primary/5 to-primary/10 border-2 border-primary/20 hover:border-primary/40 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-2xl">
              <div className="absolute top-4 right-4 bg-accent text-black px-3 py-1 rounded-full text-sm font-semibold z-30">
                Popular
              </div>
              <CardHeader className="pb-0">
                <div className="relative h-56 -mx-6 -mt-6 mb-6 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent z-10"></div>
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-20">
                    <div className="bg-primary/90 backdrop-blur-sm rounded-full p-4 transform group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.653-.121-1.28-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.653.121-1.28.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>
                  <Image 
                    src="/images/family-box.jpg" 
                    alt="Family Meal" 
                    fill 
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <CardTitle className="text-2xl lg:text-3xl font-bold text-center">Business Class</CardTitle>
              </CardHeader>
              <CardContent className="pt-4 text-center">
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Hidangan premium dengan bahan berkualiti dan penyajian eksklusif. Sempurna untuk keluarga.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-center gap-2">
                    <svg className="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>4-5 Pax</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <svg className="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Menu Premium</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <svg className="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Penghantaran Percuma</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-6">
                <Button asChild className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3 rounded-full transition-all duration-300 transform hover:scale-105">
                  <Link href="/meal-plan">Tempah Sekarang</Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* Weekly Menu Section */}
      <section className="py-20 bg-gradient-to-br from-muted via-background to-muted">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">Menu Minggu Ini:</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Nikmati pelbagai hidangan lazat yang disediakan khas untuk anda setiap hari
            </p>
          </div>
          
          <Tabs defaultValue="isnin" className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-12 bg-white/50 backdrop-blur-sm rounded-full p-1">
              <TabsTrigger value="isnin" className="rounded-full">Isnin</TabsTrigger>
              <TabsTrigger value="selasa" className="rounded-full">Selasa</TabsTrigger>
              <TabsTrigger value="rabu" className="rounded-full">Rabu</TabsTrigger>
              <TabsTrigger value="khamis" className="rounded-full">Khamis</TabsTrigger>
              <TabsTrigger value="jumaat" className="rounded-full">Jumaat</TabsTrigger>
            </TabsList>
            
            <TabsContent value="isnin" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Menu Item 1 */}
                <Card className="group overflow-hidden bg-white hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                  <div className="relative h-56 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent z-10"></div>
                    <div className="absolute top-4 left-4 bg-accent text-black px-3 py-1 rounded-full text-sm font-semibold z-20">
                      4-5 Pax
                    </div>
                    <Image 
                      src="/images/1-protein.jpg" 
                      alt="Nasi Kerabu Bucket" 
                      fill 
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-bold text-xl mb-3 line-clamp-2">Nasi Kerabu Bucket</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold text-primary">RM 96.00</div>
                      <Button asChild className="bg-primary/10 text-primary border-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <Link href="/meal-plan">View More</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Menu Item 2 */}
                <Card className="group overflow-hidden bg-white hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                  <div className="relative h-56 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent z-10"></div>
                    <div className="absolute top-4 left-4 bg-accent text-black px-3 py-1 rounded-full text-sm font-semibold z-20">
                      4-5 Pax
                    </div>
                    <Image 
                      src="/images/2-protein.jpg" 
                      alt="Nasi Hujan Panas & Ayam Masak Merah" 
                      fill 
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-bold text-xl mb-3 line-clamp-2">Nasi Hujan Panas & Ayam Masak Merah</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold text-primary">RM 110.00</div>
                      <Button asChild className="bg-primary/10 text-primary border-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <Link href="/meal-plan">View More</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                
                {/* Menu Item 3 */}
                <Card className="group overflow-hidden bg-white hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                  <div className="relative h-56 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent z-10"></div>
                    <div className="absolute top-4 left-4 bg-secondary text-white px-3 py-1 rounded-full text-sm font-semibold z-20">
                      Vegan & Kids-Friendly!
                    </div>
                    <Image 
                      src="/images/2-protein-dessert.jpeg" 
                      alt="Rice Bucket: Qhabeli Rice with Afghan Curry Eggplant" 
                      fill 
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="font-bold text-xl mb-3 line-clamp-2">Rice Bucket: Qhabeli Rice with Afghan Curry Eggplant</h3>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold text-primary">RM 110.00</div>
                      <Button asChild className="bg-primary/10 text-primary border-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <Link href="/meal-plan">View More</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="flex justify-center mt-12">
            <Button asChild className="px-10 py-3 rounded-full bg-accent hover:bg-accent/90 text-black font-semibold transition-all duration-300 transform hover:scale-105">
              <Link href="/meal-plan">Lihat Semua Menu</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* How to Order Process Section */}
      <section className="py-20 bg-gradient-to-br from-background via-primary/5 to-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">Bagaimana Saya Nak Tempah?</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Proses tempahan yang mudah dan pantas dalam 3 langkah sahaja
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12 max-w-6xl mx-auto">
            {/* Step 1 */}
            <div className="group relative bg-gradient-to-br from-accent/10 to-accent/5 rounded-2xl p-8 text-center hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
              <div className="relative h-56 mb-6 rounded-xl overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent z-10"></div>
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-20">
                  <div className="bg-accent rounded-full w-16 h-16 flex items-center justify-center text-black font-bold text-2xl shadow-lg transform group-hover:scale-110 transition-transform duration-300">
                    1
                  </div>
                </div>
                <Image 
                  src="/images/solo-box.jpg" 
                  alt="Pilih Plan Anda" 
                  fill 
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 text-foreground">Pilih Plan Anda</h3>
              <p className="text-muted-foreground leading-relaxed">
                Pilih pelan langganan yang sesuai dengan keperluan dan saiz keluarga anda.
              </p>
              <div className="absolute top-4 right-4 w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            
            {/* Step 2 */}
            <div className="group relative bg-gradient-to-br from-primary/10 to-primary/5 rounded-2xl p-8 text-center hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
              <div className="relative h-56 mb-6 rounded-xl overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent z-10"></div>
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-20">
                  <div className="bg-primary rounded-full w-16 h-16 flex items-center justify-center text-white font-bold text-2xl shadow-lg transform group-hover:scale-110 transition-transform duration-300">
                    2
                  </div>
                </div>
                <Image 
                  src="/images/1-protein.jpg" 
                  alt="Pilih Menu Anda" 
                  fill 
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 text-foreground">Pilih Menu Anda</h3>
              <p className="text-muted-foreground leading-relaxed">
                Pilih dari pelbagai menu mingguan kami yang lazat dan berkualiti tinggi.
              </p>
              <div className="absolute top-4 right-4 w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            
            {/* Step 3 */}
            <div className="group relative bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-2xl p-8 text-center hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
              <div className="relative h-56 mb-6 rounded-xl overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent z-10"></div>
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-20">
                  <div className="bg-secondary rounded-full w-16 h-16 flex items-center justify-center text-white font-bold text-2xl shadow-lg transform group-hover:scale-110 transition-transform duration-300">
                    3
                  </div>
                </div>
                <Image 
                  src="/images/family-box.jpg" 
                  alt="Kami Masak & Hantar" 
                  fill 
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <h3 className="text-xl lg:text-2xl font-bold mb-4 text-foreground">Kami Masak & Hantar</h3>
              <p className="text-muted-foreground leading-relaxed">
                Kami sediakan dan hantar makanan anda terus ke pintu rumah dengan selamat.
              </p>
              <div className="absolute top-4 right-4 w-8 h-8 bg-secondary/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
          </div>
          
          {/* Connection Lines for Desktop */}
          <div className="hidden md:block absolute inset-0 pointer-events-none">
            <div className="container mx-auto px-4 h-full flex items-center justify-center">
              <div className="flex items-center justify-between w-full max-w-4xl">
                <div className="w-8 h-0.5 bg-gradient-to-r from-accent to-primary opacity-30"></div>
                <div className="w-8 h-0.5 bg-gradient-to-r from-primary to-secondary opacity-30"></div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center mt-16">
            <Button asChild className="group relative overflow-hidden rounded-full px-12 py-4 h-auto bg-accent hover:bg-accent/90 text-black font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <Link href="/meal-plan" className="flex items-center gap-3">
                <span>Mula Tempah Sekarang</span>
                <svg className="w-5 h-5 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-muted">
        <div className="container mx-auto px-4 max-w-3xl">
          <h2 className="text-3xl font-bold mb-8 text-center">Soalan Lazim</h2>
          
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-lg font-medium">Apa itu JemputMakan?</AccordionTrigger>
              <AccordionContent>
                JemputMakan adalah perkhidmatan penghantaran makanan Muslim yang menyediakan hidangan berkualiti tinggi untuk keluarga anda. Kami fokus pada penyediaan makanan yang sihat, lazat dan menjimatkan masa anda.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-2">
              <AccordionTrigger className="text-lg font-medium">Bagaimana JemputMakan membantu anda?</AccordionTrigger>
              <AccordionContent>
                JemputMakan membantu anda menjimatkan masa dan wang dengan menyediakan hidangan yang siap dimasak dan dihantar terus ke rumah anda. Anda tidak perlu lagi risau tentang perancangan menu, membeli bahan, atau memasak selepas hari yang memenatkan.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-3">
              <AccordionTrigger className="text-lg font-medium">Kawasan penghantaran?</AccordionTrigger>
              <AccordionContent>
                Kami kini menghantar ke seluruh Lembah Klang termasuk Kuala Lumpur, Petaling Jaya, Shah Alam, Subang Jaya, dan kawasan sekitarnya. Sila hubungi kami untuk maklumat lanjut tentang kawasan penghantaran anda.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-4">
              <AccordionTrigger className="text-lg font-medium">Bagaimana saya nak tempah?</AccordionTrigger>
              <AccordionContent>
                Tempahan boleh dibuat melalui laman web kami dengan memilih plan, menu dan tarikh penghantaran yang anda inginkan. Pembayaran boleh dibuat secara online melalui kad kredit, debit atau e-wallet.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-5">
              <AccordionTrigger className="text-lg font-medium">Boleh saya tukar menu?</AccordionTrigger>
              <AccordionContent>
                Ya, anda boleh menukar menu pilihan anda sehingga 48 jam sebelum tarikh penghantaran. Sila hubungi khidmat pelanggan kami untuk membuat perubahan pada tempahan anda.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Apa Kata Pelanggan?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="bg-white transform hover:scale-105 transition-transform duration-300">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center mb-4">
                  <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                    <svg className="w-8 h-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-lg">Azizah Rahman</h3>
                  <div className="text-sm text-muted-foreground">Pelanggan Tetap</div>
                </div>
                <p className="text-center italic">JemputMakan telah menyelamatkan masa saya setiap hari. Makanan mereka lazat dan keluarga saya sangat menyukainya. Sangat menjimatkan masa dan wang!</p>
              </CardContent>
            </Card>
            
            {/* Testimonial 2 */}
            <Card className="bg-white transform hover:scale-105 transition-transform duration-300">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center mb-4">
                  <div className="w-16 h-16 rounded-full bg-secondary/20 flex items-center justify-center mb-2">
                    <svg className="w-8 h-8 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-lg">Farid Ismail</h3>
                  <div className="text-sm text-muted-foreground">Pelanggan Baru</div>
                </div>
                <p className="text-center italic">Kualiti makanan yang sangat baik dan perkhidmatan penghantaran yang tepat pada masanya. Saya sangat mengesyorkan JemputMakan kepada sesiapa yang ingin menjimatkan masa.</p>
              </CardContent>
            </Card>
            
            {/* Testimonial 3 */}
            <Card className="bg-white transform hover:scale-105 transition-transform duration-300">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center mb-4">
                  <div className="w-16 h-16 rounded-full bg-accent/20 flex items-center justify-center mb-2">
                    <svg className="w-8 h-8 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-lg">Nurul Huda</h3>
                  <div className="text-sm text-muted-foreground">Pelanggan 6 Bulan</div>
                </div>
                <p className="text-center italic">Sebagai ibu bekerja, JemputMakan telah menjadi penyelamat bagi saya. Makanan yang sihat dan lazat untuk keluarga tanpa perlu memasak setiap hari!</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-accent text-secondary py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center md:items-start">
              <Image 
                src="/images/jemput_makan_logo.png" 
                alt="JemputMakan Logo" 
                width={250} 
                height={150} 
                className="mb-4"
              />
              <p className="mb-6 text-secondary-hover text-center md:text-left">Kami Urus Pemakanan Anda & Keluarga Anda Dengan Penjimatan Masa & Wang</p>
              <div className="flex items-center space-x-4">
                <a href="#" className="text-secondary hover:text-secondary-hover">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="#" className="text-secondary hover:text-secondary-hover">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-bold mb-4">Hubungi Kami</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-secondary mt-1 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <div>
                    <p className="font-medium">Phone</p>
                    <p className="text-secondary-hover">013-2372920</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-secondary mt-1 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <div>
                    <p className="font-medium">Customer Service</p>
                    <p className="text-secondary-hover">013-2372920</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-secondary mt-1 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-secondary-hover"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center secondary-hover">
            <div className="space-y-2">
              <p>&copy; {new Date().getFullYear()} JemputMakan. All rights reserved.</p>
              <p className="text-sm text-secondary-hover">
                GANGCO VENTURES | Reg No: 202403039129 (CT0132720-P)
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
