import React from 'react';
import { useMealPlan } from '@/context/MealPlanContext';
import { NavigationButtons } from '../navigation/NavigationButtons';

interface ContactInfoProps {
  onNext: () => void;
  onBack: () => void;
}

const ContactInfo: React.FC<ContactInfoProps> = ({ onNext, onBack }) => {
  const { state, setState } = useMealPlan();
  const [formData, setFormData] = React.useState({
    name: state.userDetails?.name || '',
    phone: state.userDetails?.phone || '',
    address1: state.userDetails?.address1 || '',
    address2: state.userDetails?.address2 || '',
    postcode: state.userDetails?.postcode || '',
    state: state.userDetails?.state || '',
    country: state.userDetails?.country || 'Malaysia',
  });

  const isValid = 
    formData.name && 
    formData.phone && 
    formData.address1 && 
    formData.postcode && 
    formData.state && 
    formData.country;

  const handleSubmit = () => {
    if (isValid) {
      setState({ userDetails: formData });
      onNext();
    }
  };

  return (
    <div className="flex flex-col justify-between min-h-[calc(100dvh-100px)]">
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="flex-grow space-y-8 pb-4">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-foreground mb-2">Contact Information</h2>
          <p className="text-muted-foreground">Please provide your contact details for delivery</p>
        </div>
        
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Personal Information Section */}
          <div className="form-container bg-background border border-border rounded-lg p-6 space-y-6">
            <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
              Personal Information
            </h3>
            
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Full Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="input-base w-full"
                required
                placeholder="Enter your full name"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="input-base w-full"
                required
                placeholder="Enter your phone number"
                pattern="[0-9]{10,11}"
              />
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Format: 0123456789 (10-11 digits)
              </p>
            </div>
          </div>

          {/* Delivery Address Section */}
          <div className="form-container bg-background border border-border rounded-lg p-6 space-y-6">
            <h3 className="text-lg font-semibold text-foreground border-b border-border pb-2">
              Delivery Address
            </h3>
            
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Address Line 1 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.address1}
                onChange={(e) => setFormData({ ...formData, address1: e.target.value })}
                className="input-base w-full"
                required
                placeholder="Street address, building name"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Address Line 2
              </label>
              <input
                type="text"
                value={formData.address2}
                onChange={(e) => setFormData({ ...formData, address2: e.target.value })}
                className="input-base w-full"
                placeholder="Apartment, suite, unit number (optional)"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-foreground">
                  Postcode <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.postcode}
                  onChange={(e) => setFormData({ ...formData, postcode: e.target.value })}
                  className="input-base w-full"
                  required
                  placeholder="12345"
                  maxLength={5}
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-foreground">
                  State <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.state}
                  onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                  className="input-base w-full"
                  required
                >
                  <option value="">Select State</option>
                  <option value="Johor">Johor</option>
                  <option value="Kedah">Kedah</option>
                  <option value="Kelantan">Kelantan</option>
                  <option value="Kuala Lumpur">Kuala Lumpur</option>
                  <option value="Labuan">Labuan</option>
                  <option value="Melaka">Melaka</option>
                  <option value="Negeri Sembilan">Negeri Sembilan</option>
                  <option value="Pahang">Pahang</option>
                  <option value="Penang">Penang</option>
                  <option value="Perak">Perak</option>
                  <option value="Perlis">Perlis</option>
                  <option value="Putrajaya">Putrajaya</option>
                  <option value="Sabah">Sabah</option>
                  <option value="Sarawak">Sarawak</option>
                  <option value="Selangor">Selangor</option>
                  <option value="Terengganu">Terengganu</option>
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Country
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.country}
                  className="input-base w-full bg-muted text-muted-foreground cursor-not-allowed"
                  disabled
                  readOnly
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <svg className="w-4 h-4 text-muted-foreground" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Currently only available in Malaysia
              </p>
            </div>
          </div>
        </div>
      </form>

      <NavigationButtons 
        onBack={onBack}
        onNext={handleSubmit}
        nextDisabled={!isValid}
        nextText="Review & Pay"
      />
    </div>
  );
};

export default ContactInfo;