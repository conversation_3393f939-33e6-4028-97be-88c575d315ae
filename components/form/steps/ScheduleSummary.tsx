import React from 'react';
// import StartDatePicker from './elements/StartDatePicker';
// import SummaryStep from './elements/SummaryStep';
import { NavigationButtons } from '../navigation/NavigationButtons';
import ContactInfoStep from '@/components/form/steps/elements/ContactInfoStep';

const ScheduleSummary: React.FC<{ onNext: () => void; onBack: () => void }> = ({
  onNext,
  onBack,
}) => {

  return (
    <div className="flex flex-col justify-between">
      <div className="space-y-8 pb-8 mb-8">
        <h2 className="text-2xl font-bold text-black">Review</h2>
        
        <div className="space-y-6">

          <div className="bg-white p-4 rounded-lg space-y-4">
            <h3 className="text-lg font-semibold">Contact Details</h3>
            <div className="space-y-2">
              <ContactInfoStep 
                onNext={onNext} 
                onBack={onBack} 
              />
            </div>
          </div>

          {/* <div className="bg-white p-4 rounded-lg">
            <SummaryStep />
          </div> */}

        </div>
      </div>

      <NavigationButtons onBack={onBack} onNext={onNext} />
    </div>
  );
};

export default ScheduleSummary; 