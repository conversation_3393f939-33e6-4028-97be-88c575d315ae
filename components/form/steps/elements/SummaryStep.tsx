import React from 'react';
import { useMealPlan } from '@/context/MealPlanContext';
import { capitalize } from '@/utils/helpers';
import { calculateTotalPrice } from '@/utils/calculations';

const SummaryStep: React.FC = ({
}) => {
  const { state } = useMealPlan();
  
  return (
    <div className="flex flex-col justify-between">
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-black">Review Your Meal Plan</h2>
        
        {/* Meal Plan Container */}
        <div className="bg-white rounded-lg p-1 space-y-3">
          
          <div className="space-y-3">
            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Number of People:</span>
              <span className="text-appBlue font-semibold">{state.pax}</span>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Subscription Duration:</span>
              <span className="text-appBlue font-semibold">
                {state.subscriptionDuration} day(s)
              </span>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Meal Option:</span>
              <span className="text-appBlue font-semibold">
                {capitalize(state.mealOption || '')}
              </span>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Package:</span>
              <span className="text-appBlue font-semibold">
                {state.diet?.selectedPackage 
                  ? capitalize(state.diet.selectedPackage) + ' Plan'
                  : 'Not selected'}
              </span>
            </div>

            {/* Diet Section */}
            <div className="space-y-2 pt-2">
              <div className="flex justify-between items-center pb-2 border-b border-gray-200">
                <span className="text-black">Protein:</span>
                <span className="text-appBlue font-semibold">
                  {state.diet?.includes?.protein || 0} Protein
                </span>
              </div>
              <div className="flex justify-between items-center pb-2 border-b border-gray-200">
                <span className="text-black">Veggies:</span>
                <span className="text-appBlue font-semibold">
                  {state.diet?.includes?.veggies || 0} {(state.diet?.includes?.veggies || 0) === 1 ? 'Veggie' : 'Veggies'}
                </span>
              </div>
              <div className="flex justify-between items-center pb-2 border-b border-gray-200">
                <span className="text-black">Side:</span>
                <span className="text-appBlue font-semibold">
                  {state.diet?.includes?.side || 0} Side
                </span>
              </div>
              <div className="flex justify-between items-center pb-2 border-b border-gray-200">
                <span className="text-black">Dessert:</span>
                <span className="text-appBlue font-semibold">
                  {state.diet?.includes?.dessert || 0} Dessert
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Rice Included:</span>
              <span className="text-appBlue font-semibold">
                {state.riceIncluded ? 'Yes' : 'No'}
              </span>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Start Date:</span>
              <span className="text-appBlue font-semibold">
                {state.startDate ? new Date(state.startDate).toLocaleDateString() : 'Not selected'}
              </span>
            </div>
          </div>

          <div className="mt-6 pt-5 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-black font-bold">Total Price:</span>
              <span className="text-green-600 font-bold text-xl">
                RM {calculateTotalPrice(state).toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Address Container */}
        <div className="bg-white rounded-lg p-4 space-y-4 mt-6">
          <h3 className="text-lg font-semibold">Contact Information</h3>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Name:</span>
              <span className="text-appBlue font-semibold">
                {state.userDetails?.name || 'Not provided'}
              </span>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Phone:</span>
              <span className="text-appBlue font-semibold">
                {state.userDetails?.phone || 'Not provided'}
              </span>
            </div>

            <div className="flex justify-between items-center pb-2 border-b border-gray-200">
              <span className="text-black">Address:</span>
              <div className="text-right">
                <p className="text-appBlue font-semibold">{state.userDetails?.address1}</p>
                {state.userDetails?.address2 && (
                  <p className="text-appBlue font-semibold">{state.userDetails.address2}</p>
                )}
                <p className="text-appBlue font-semibold">
                  {state.userDetails?.postcode} {state.userDetails?.state}
                </p>
                <p className="text-appBlue font-semibold">{state.userDetails?.country}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default SummaryStep; 