import React, { useState, useEffect } from 'react';
// import Image from 'next/image';
import { useMealPlan } from '@/context/MealPlanContext';
import { ApiClient } from '@/lib/api-client';

interface MealPlan {
  id: string;
  name: string;
  description: string;
  price: string;
  is_active: boolean;
}

interface MealPlanResponse {
  meal_plans: MealPlan[];
}

const DietTypeSelection: React.FC = () => {
  const { state, setState } = useMealPlan();
  const [mealPackages, setMealPackages] = useState<MealPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMealPlans = async () => {
      try {
        // Test connection first
        const isConnected = await ApiClient.testConnection();
        if (!isConnected) {
          throw new Error('Cannot connect to the server. Please check if your Laravel backend is running.');
        }

        // Fetch meal plans using the enhanced API client
        const data = await ApiClient.get('/meal-plans') as MealPlanResponse;

        // Filter only active meal plans
        const activeMealPlans = data.meal_plans.filter(plan => plan.is_active);
        setMealPackages(activeMealPlans);
      } catch (err) {
        console.error('Error fetching meal plans:', err);

        // Use enhanced error details from ApiClient
        const errorMessage = ApiClient.getErrorDetails(err);
        setError(errorMessage);

        // Fallback to empty array if API fails
        setMealPackages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMealPlans();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-foreground">Select Your Package</h2>
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-foreground">Select Your Package</h2>
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">Error loading meal plans: {error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (mealPackages.length === 0) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-foreground">Select Your Package</h2>
        <div className="text-center py-8">
          <p className="text-muted-foreground">No meal plans available at the moment.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-foreground">Select Your Package</h2>
      <div className="grid gap-4">
        {mealPackages.map((pkg) => (
          <div
            key={pkg.id}
            className={`form-container cursor-pointer transition-all duration-200 border-2 rounded-xl p-2
              ${state.diet?.selectedPackage === pkg.id 
                ? 'border-primary bg-appBlueHover shadow-sm' 
                : 'border-border hover:border-green-300'}`}
            onClick={() => setState({
              diet: { selectedPackage: pkg.id as 'basic' | 'premium' | 'deluxe' }
            })}
          >
            <div className="flex items-start gap-4">
              <div className="flex-grow">
                <h3 className="font-semibold text-lg mb-1">{pkg.name}</h3>
                <p className="text-muted-foreground text-sm mb-3">{pkg.description}</p>
                
                <div className="flex items-center gap-2 text-sm">
                  <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Complete meal plan included</span>
                </div>
              </div>
              
              <div className="flex flex-col items-end gap-2">
                <div className="text-lg font-bold text-appBlue">RM{pkg.price}</div>
                <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center
                  ${state.diet?.selectedPackage === pkg.id 
                    ? 'border-appBlue bg-primary' 
                    : 'border-border'}`}
                >
                  {state.diet?.selectedPackage === pkg.id && (
                    <svg className="w-4 h-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DietTypeSelection;