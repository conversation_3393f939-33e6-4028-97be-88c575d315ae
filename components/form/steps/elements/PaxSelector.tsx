import React from 'react';
import { useMealPlan } from '@/context/MealPlanContext';

const PaxSelector: React.FC = () => {
  const { state, setState } = useMealPlan();
  // Initialize pax if undefined, default to 2
  React.useEffect(() => {
    if (state.pax === undefined || state.pax === null) { // More robust check
      setState({ pax: 2 });
    }
  }, [setState, state.pax]);

  const handlePaxChange = (newPax: number) => {
    console.log('Current pax:', state.pax);
    console.log('New pax:', newPax);
    if (newPax >= 1 && newPax <= 10) {
      setState({ pax: newPax });
    }
  };

  // Ensure pax has a value, default to 2
  const pax = state.pax === undefined || state.pax === null ? 2 : state.pax;

  return (
    <div className="form-container">
      <h2 className="text-2xl font-bold text-foreground mb-6">How many people are you prepping for?</h2>
      
      <div className="space-y-8">
        {/* Number Display */}
        <div className="text-center">
          <span className="text-6xl font-bold text-black">{pax}</span>
          <p className="text-muted-foreground mt-2">
            {pax === 1 ? 'Person' : 'People'}
          </p>
        </div>

        {/* Slider */}
        <div className="px-4 select-none">
          {/* Slider container with fixed height for thumb positioning */}
          <div className="relative h-6">
            {/* Background track - light gray or transparent */}
            <div className="absolute h-2 bg-gray-200 rounded-lg w-full top-1/2 -translate-y-1/2"></div>
            
            {/* Filled track - green color only up to the current position */}
            <div 
              className="absolute h-2 bg-green-300 rounded-l-lg" 
              style={{ 
                width: `${((pax - 1) / 9) * 100}%`,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 1
              }}
            />
            <input
              type="range"
              min="1"
              max="10"
              value={pax}
              onChange={(e) => handlePaxChange(parseInt(e.target.value))}
              onTouchMove={(e) => e.preventDefault()}
              className="absolute w-full h-full bg-transparent rounded-lg appearance-none cursor-pointer z-10 top-1/2 -translate-y-1/2
                [&::-webkit-slider-thumb]:appearance-none
                [&::-webkit-slider-thumb]:w-6
                [&::-webkit-slider-thumb]:h-6
                [&::-webkit-slider-thumb]:rounded-full
                [&::-webkit-slider-thumb]:bg-green-300 
                [&::-webkit-slider-thumb]:border-[3px]
                [&::-webkit-slider-thumb]:border-app-blue
                [&::-webkit-slider-thumb]:shadow-md
                [&::-webkit-slider-thumb]:hover:border-app-blue/80
                [&::-webkit-slider-thumb]:transition-colors
                [&::-webkit-slider-thumb]:z-10
                [&::-webkit-slider-thumb]:relative"
            />
          </div>
          {/* Tick marks with current value indicator - moved outside and below the slider mechanism */}
          <div className="flex justify-between text-xs text-muted-foreground mt-2">
            <span className={pax === 1 ? "text-app-blue font-medium" : ""}>1</span>
            <span className={pax === 2 ? "text-app-blue font-medium" : ""}>2</span>
            <span className={pax === 3 ? "text-app-blue font-medium" : ""}>3</span>
            <span className={pax === 4 ? "text-app-blue font-medium" : ""}>4</span>
            <span className={pax === 5 ? "text-app-blue font-medium" : ""}>5</span>
            <span className={pax === 6 ? "text-app-blue font-medium" : ""}>6</span>
            <span className={pax === 7 ? "text-app-blue font-medium" : ""}>7</span>
            <span className={pax === 8 ? "text-app-blue font-medium" : ""}>8</span>
            <span className={pax === 9 ? "text-app-blue font-medium" : ""}>9</span>
            <span className={pax === 10 ? "text-app-blue font-medium" : ""}>10</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center items-center gap-6">
          <button
            type="button"
            onClick={() => handlePaxChange(pax - 1)}
            className={`p-3 rounded-full transition-colors
              ${pax > 1 
                ? 'bg-muted hover:bg-muted-foreground/20 text-foreground' 
                : 'bg-muted/50 text-muted-foreground cursor-not-allowed'}`}
            disabled={pax <= 1}
            aria-label="Decrease number of people"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>

          <button
            type="button"
            onClick={() => handlePaxChange(pax + 1)}
            className={`p-3 rounded-full transition-colors
              ${pax < 10 
                ? 'bg-muted hover:bg-muted-foreground/20 text-foreground' 
                : 'bg-muted/50 text-muted-foreground cursor-not-allowed'}`}
            disabled={pax >= 10}
            aria-label="Increase number of people"
          >
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>

        {/* Size Description */}
        <div className="text-center text-sm text-muted-foreground">
          {pax === 1 && "Perfect for individual meal planning"}
          {pax >= 2 && pax <= 4 && "Great for small families"}
          {pax >= 5 && pax <= 7 && "Ideal for larger families"}
          {pax >= 8 && "Perfect for gatherings or meal prep"}
        </div>
      </div>
    </div>
  );
};

export default PaxSelector;