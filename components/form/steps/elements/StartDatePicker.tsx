import React, { useEffect } from 'react';
import { useMealPlan } from '@/context/MealPlanContext';

const StartDatePicker: React.FC = () => {
  const { state, setState } = useMealPlan();

  // Get tomorrow's date
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  // Prefill with tomorrow's date if no date is selected
  useEffect(() => {
    if (!state.startDate) {
      setState({ startDate: minDate });
    }
  }, [state.startDate, setState, minDate]);

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="form-container">
      <h2 className="text-2xl font-bold text-foreground mb-6">When would you like to start?</h2>
      
      <div className="space-y-6">
        {/* Date Display */}
        <div className="text-center">
          {state.startDate ? (
            <div className="space-y-2">
              <p className="text-lg text-muted-foreground">Your meals will start on</p>
              <p className="text-2xl font-semibold text-primary">{formatDate(state.startDate)}</p>
            </div>
          ) : (
            <p className="text-lg text-muted-foreground">Select a start date</p>
          )}
        </div>

        {/* Date Input */}
        <div className="relative">
          <input
            type="date"
            value={state.startDate || ''}
            min={minDate}
            onChange={(e) => setState({ startDate: e.target.value })}
            className="input-base w-full text-lg py-4 px-5"
          />
          <div className="absolute right-4 top-1/2 -translate-y-1/2 pointer-events-none">
          </div>
        </div>

        {/* Helper Text */}
        <p className="text-sm text-muted-foreground text-center">
          <strong>Pre-order only:</strong> We need at least one day to prepare your meals.
          <br />
          Delivery times may vary based on your location.
        </p>
      </div>
    </div>
  );
};

export default StartDatePicker; 