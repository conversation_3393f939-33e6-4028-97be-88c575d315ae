import React from 'react';
import { useMealPlan } from '@/context/MealPlanContext';
import LunchDinnerToggle from './elements/LunchDinnerToggle';
import DietTypeSelection from './elements/DietTypeSelection';
import RiceOptionToggle from './elements/RiceOptionToggle';
import { NavigationButtons } from '../navigation/NavigationButtons';

const MealPreferences: React.FC<{ onNext: () => void; onBack: () => void }> = ({
  onNext,
  onBack,
}) => {
  const { state } = useMealPlan();

  const isValid = 
    state.mealOption && // Check if meal option is selected
    state.diet?.selectedPackage && // Check if package is selected
    typeof state.riceIncluded === 'boolean'; // Check if rice option is selected

  const handleNext = () => {
    if (isValid) {
      onNext();
    }
  };

  return (
    <div className="flex flex-col justify-between min-h-[calc(100dvh-100px)]">
      <div className="space-y-8 pb-5 mb-5">
        <h2 className="text-2xl font-bold text-black">Meal Preferences</h2>
        
        <div className="space-y-6">
          <div className="bg-white p-4 rounded-lg">
            <LunchDinnerToggle />
          </div>

          <div className="bg-white p-4 rounded-lg">
            <DietTypeSelection/>
          </div>

          <div className="bg-white p-4 rounded-lg">
            <RiceOptionToggle />
          </div>
        </div>
      </div>

      <NavigationButtons 
        onBack={onBack} 
        onNext={handleNext}
        nextDisabled={!isValid}
      />
    </div>
  );
};

export default MealPreferences;