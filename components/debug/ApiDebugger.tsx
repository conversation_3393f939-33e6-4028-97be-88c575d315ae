'use client';

import React, { useState } from 'react';
import { ApiClient } from '@/lib/api-client';

const ApiDebugger: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testApiConnection = async () => {
    setIsLoading(true);
    clearResults();
    
    try {
      addResult('🔍 Starting API connection test...');
      
      // Test 1: Check environment variable
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;
      addResult(`📋 API URL from environment: ${apiUrl || 'NOT SET'}`);
      
      if (!apiUrl) {
        addResult('❌ NEXT_PUBLIC_API_URL is not set in environment variables');
        return;
      }
      
      // Test 2: Test basic connectivity
      addResult('🌐 Testing basic connectivity...');
      const isConnected = await ApiClient.testConnection();
      addResult(`🔗 Connection test result: ${isConnected ? '✅ SUCCESS' : '❌ FAILED'}`);
      
      if (!isConnected) {
        addResult('💡 Possible issues:');
        addResult('   - Laravel backend is not running');
        addResult('   - CORS is not properly configured');
        addResult('   - API URL is incorrect');
        addResult('   - Network connectivity issues');
        return;
      }
      
      // Test 3: Try to fetch meal plans
      addResult('📊 Testing meal plans endpoint...');
      try {
        const data = await ApiClient.get('/meal-plans');
        addResult('✅ Meal plans endpoint responded successfully');
        addResult(`📦 Response data: ${JSON.stringify(data, null, 2)}`);
      } catch (error) {
        addResult(`❌ Meal plans endpoint failed: ${ApiClient.getErrorDetails(error)}`);
      }
      
    } catch (error) {
      addResult(`❌ Test failed: ${ApiClient.getErrorDetails(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSpecificEndpoint = async () => {
    setIsLoading(true);
    
    try {
      addResult('🎯 Testing specific endpoint...');
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/meal-plans`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });
      
      addResult(`📡 Response status: ${response.status} ${response.statusText}`);
      addResult(`📋 Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
      
      if (response.ok) {
        const data = await response.json();
        addResult(`✅ Response data: ${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        addResult(`❌ Error response: ${errorText}`);
      }
      
    } catch (error) {
      addResult(`❌ Direct fetch failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">🔧 API Connection Debugger</h3>
      
      <div className="space-x-4 mb-4">
        <button
          onClick={testApiConnection}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test API Connection'}
        </button>
        
        <button
          onClick={testSpecificEndpoint}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test Direct Fetch'}
        </button>
        
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Clear Results
        </button>
      </div>
      
      <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
        {testResults.length === 0 ? (
          <div className="text-gray-500">Click &quot;Test API Connection&quot; to start debugging...</div>
        ) : (
          testResults.map((result, index) => (
            <div key={index} className="mb-1">
              {result}
            </div>
          ))
        )}
      </div>
      
      <div className="mt-4 text-sm text-gray-600">
        <p><strong>Environment Info:</strong></p>
        <p>API URL: {process.env.NEXT_PUBLIC_API_URL || 'NOT SET'}</p>
        <p>Node Environment: {process.env.NODE_ENV}</p>
      </div>
    </div>
  );
};

export default ApiDebugger;
