'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { useComingSoon } from '@/context/ComingSoonContext';
import { COMING_SOON_CONFIG } from '@/lib/config/coming-soon';

const ComingSoon: React.FC = () => {
  const { grantAccess } = useComingSoon();
  const [keySequence, setKeySequence] = useState('');
  const [clickCount, setClickCount] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);

  // Handle key sequence detection
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!COMING_SOON_CONFIG.ACCESS_METHODS.KEY_SEQUENCE.enabled) return;

    const newSequence = keySequence + event.key.toLowerCase();
    const targetSequence = COMING_SOON_CONFIG.ACCESS_METHODS.KEY_SEQUENCE.sequence;

    if (targetSequence.startsWith(newSequence)) {
      setKeySequence(newSequence);
      
      if (newSequence === targetSequence) {
        grantAccess();
        setKeySequence('');
      }
    } else {
      setKeySequence('');
    }
  }, [keySequence, grantAccess]);

  // Handle logo click sequence
  const handleLogoClick = () => {
    if (!COMING_SOON_CONFIG.ACCESS_METHODS.CLICK_SEQUENCE.enabled) return;

    const now = Date.now();
    const timeWindow = COMING_SOON_CONFIG.ACCESS_METHODS.CLICK_SEQUENCE.timeWindow;
    const requiredClicks = COMING_SOON_CONFIG.ACCESS_METHODS.CLICK_SEQUENCE.clicks;

    if (now - lastClickTime > timeWindow) {
      // Reset if too much time has passed
      setClickCount(1);
    } else {
      setClickCount(prev => prev + 1);
    }

    setLastClickTime(now);

    if (clickCount + 1 >= requiredClicks) {
      grantAccess();
      setClickCount(0);
    }
  };

  // Set up keyboard listener
  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-primary/10 via-background to-accent/10">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <Image
          src={COMING_SOON_CONFIG.CONTENT.backgroundImage}
          alt="Background"
          fill
          className="object-cover opacity-20"
          priority
        />
        <div className="absolute inset-0 bg-background/80" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex min-h-screen items-center justify-center p-4">
        <div className="max-w-2xl text-center">
          {/* Logo */}
          <div className="mb-8 flex justify-center">
            <div 
              className="cursor-pointer transition-transform hover:scale-105"
              onClick={handleLogoClick}
              title="Logo"
            >
              <Image
                src={COMING_SOON_CONFIG.CONTENT.logo}
                alt={COMING_SOON_CONFIG.CONTENT.brandName}
                width={200}
                height={200}
                className="h-32 w-auto object-contain"
                priority
              />
            </div>
          </div>

          {/* Brand Name */}
          <h1 className="mb-4 text-6xl font-bold text-primary">
            {COMING_SOON_CONFIG.CONTENT.brandName}
          </h1>

          {/* Tagline */}
          <p className="mb-6 text-xl text-muted-foreground">
            {COMING_SOON_CONFIG.CONTENT.tagline}
          </p>

          {/* Coming Soon Message */}
          <div className="mb-8 rounded-lg bg-white/50 p-6 shadow-lg backdrop-blur-sm">
            <h2 className="mb-4 text-3xl font-semibold text-foreground">
              Coming Soon
            </h2>
            <p className="text-lg text-muted-foreground">
              {COMING_SOON_CONFIG.CONTENT.message}
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="mx-auto mb-2 h-2 w-64 rounded-full bg-muted">
              <div className="h-2 w-3/4 rounded-full bg-gradient-to-r from-primary to-accent"></div>
            </div>
            <p className="text-sm text-muted-foreground">75% Complete</p>
          </div>

          {/* Social Links / Contact Info */}
          <div className="space-y-2 text-muted-foreground">
            <p className="text-sm">
              Follow us for updates and be the first to know when we launch!
            </p>
            <div className="flex justify-center space-x-4">
              <span className="text-sm">📧 <EMAIL></span>
              <span className="text-sm">📱 Follow us on social media</span>
            </div>
          </div>

          {/* Hidden Access Hint (only visible in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8 rounded border border-dashed border-muted-foreground/30 p-4">
              <p className="text-xs text-muted-foreground">
                <strong>Dev Mode Access Hints:</strong><br />
                • Type &quot;admin&quot; anywhere on this page<br />
                • Click the logo {COMING_SOON_CONFIG.ACCESS_METHODS.CLICK_SEQUENCE.clicks} times quickly<br />
                • Add ?preview=true to the URL<br />
                • Visit /preview directly
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Floating Elements for Visual Interest */}
      <div className="absolute left-10 top-20 h-20 w-20 rounded-full bg-primary/10 blur-xl"></div>
      <div className="absolute right-20 top-40 h-32 w-32 rounded-full bg-accent/10 blur-xl"></div>
      <div className="absolute bottom-20 left-1/4 h-24 w-24 rounded-full bg-secondary/10 blur-xl"></div>
    </div>
  );
};

export default ComingSoon;
