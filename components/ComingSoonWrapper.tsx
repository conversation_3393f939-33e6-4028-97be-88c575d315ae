'use client';

import React from 'react';
import { useComingSoon } from '@/context/ComingSoonContext';
import ComingSoon from './ComingSoon';

interface ComingSoonWrapperProps {
  children: React.ReactNode;
}

const ComingSoonWrapper: React.FC<ComingSoonWrapperProps> = ({ children }) => {
  const { showComingSoon } = useComingSoon();

  if (showComingSoon) {
    return <ComingSoon />;
  }

  return <>{children}</>;
};

export default ComingSoonWrapper;
