'use client';

import React from 'react';
import { revokeAccess, isAccessValid } from '@/lib/config/coming-soon';
import { useComingSoon } from '@/context/ComingSoonContext';

const AdminAccess: React.FC = () => {
  const { checkAccess } = useComingSoon();
  const [hasAccess, setHasAccess] = React.useState(false);

  React.useEffect(() => {
    setHasAccess(isAccessValid());
  }, []);

  const handleRevokeAccess = () => {
    revokeAccess();
    setHasAccess(false);
    checkAccess(); // This will trigger the coming soon page to show
  };

  if (!hasAccess) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="rounded-lg bg-red-500 p-3 text-white shadow-lg">
        <p className="mb-2 text-sm font-medium">Admin Access Active</p>
        <button
          onClick={handleRevokeAccess}
          className="rounded bg-red-600 px-3 py-1 text-xs hover:bg-red-700"
        >
          Revoke Access
        </button>
      </div>
    </div>
  );
};

export default AdminAccess;
