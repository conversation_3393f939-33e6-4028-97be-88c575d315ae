'use client';

import { useState } from 'react';
import { getMealPlans, MealPlan } from '@/lib/services';
import { Button } from '@/components/ui/button';

export default function ApiExample() {
  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMealPlans = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await getMealPlans();
      setMealPlans(data);
    } catch (err) {
      setError('Failed to fetch meal plans. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">API Example</h2>
      
      <Button 
        onClick={fetchMealPlans}
        disabled={loading}
        className="mb-6"
      >
        {loading ? 'Loading...' : 'Fetch Meal Plans'}
      </Button>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {mealPlans.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mealPlans.map((plan) => (
            <div 
              key={plan.id} 
              className="border rounded-lg p-4 shadow-sm"
            >
              <h3 className="text-xl font-semibold">{plan.name}</h3>
              <p className="text-gray-600 mt-2">{plan.description}</p>
              <p className="text-lg font-bold mt-2">${plan.price.toFixed(2)}</p>
            </div>
          ))}
        </div>
      ) : !loading && (
        <p className="text-gray-500">No meal plans to display. Click the button to fetch data.</p>
      )}
    </div>
  );
}