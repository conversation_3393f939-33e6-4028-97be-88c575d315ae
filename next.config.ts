import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable static export
  output: 'export',

  // Disable server-side features for static export
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Image optimization must be disabled for static export
  images: {
    unoptimized: true,
  },

  // Additional security configurations (compatible with static export)
  poweredByHeader: false, // Remove X-Powered-By header

  // Note: headers(), redirects(), rewrites() are not supported in static export
  // Security headers should be configured at the hosting level (Vercel, Netlify, etc.)
};

export default nextConfig;
