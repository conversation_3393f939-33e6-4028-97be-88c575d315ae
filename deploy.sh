#!/bin/bash

# Deployment script for Next.js static export to production branch
# This script builds the static site and deploys it to a separate production branch

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository!"
    exit 1
fi

# Get current branch name
CURRENT_BRANCH=$(git branch --show-current)
print_status "Current branch: $CURRENT_BRANCH"

# Step 1: Ensure we're on main branch and up to date
print_status "Step 1: Ensuring we're on main branch and up to date..."

if [ "$CURRENT_BRANCH" != "main" ]; then
    print_status "Switching to main branch..."
    git checkout main
fi

# Check if there are uncommitted changes
if ! git diff-index --quiet HEAD --; then
    print_error "You have uncommitted changes on main branch. Please commit or stash them first."
    exit 1
fi

# Pull latest changes from remote main
print_status "Pulling latest changes from remote main..."
if git remote | grep -q origin; then
    git pull origin main
else
    print_warning "No 'origin' remote found. Skipping pull."
fi

# Step 2: Run Next.js build + export
print_status "Step 2: Building and exporting Next.js application..."

# Clean previous build
if [ -d "out" ]; then
    print_status "Cleaning previous build output..."
    rm -rf out
fi

# Check if next.config has output: 'export' configured
if ! grep -q "output.*export" next.config.* 2>/dev/null; then
    print_error "next.config.js/ts must have 'output: \"export\"' configured for static export!"
    print_error "Please add 'output: \"export\"' to your Next.js config."
    exit 1
fi

# Run build (which will also export when output: 'export' is set)
print_status "Running npm run build (with static export)..."
npm run build

# Verify out directory exists and has content
if [ ! -d "out" ] || [ -z "$(ls -A out)" ]; then
    print_error "Build failed or out directory is empty!"
    exit 1
fi

print_success "Build and export completed successfully!"

# Step 2.5: Verify _next directory structure
print_status "Step 2.5: Verifying _next directory structure..."

# Check if _next directory exists
if [ -d "out/_next" ]; then
    print_success "_next directory found with proper structure"
else
    print_warning "_next directory not found in out directory"
fi

# Create a temporary directory to store the build output
TEMP_OUT_DIR=$(mktemp -d)
print_status "Copying build output to temporary directory: $TEMP_OUT_DIR"
# Copy only the contents of the out directory, not the directory itself
if [ -d "out" ] && [ -n "$(ls -A out)" ]; then
    cp -r out/* "$TEMP_OUT_DIR/" 2>/dev/null || true
    # Copy hidden files if any (but exclude . and ..)
    find out -name ".*" -not -name "." -not -name ".." -exec cp -r {} "$TEMP_OUT_DIR/" \; 2>/dev/null || true
else
    print_error "Out directory is empty or doesn't exist!"
    exit 1
fi

# Step 3: Check for production worktree and handle deployment
print_status "Step 3: Checking production branch setup..."

# Get the absolute path to the main project directory
MAIN_PROJECT_DIR="/Users/<USER>/familimeal-frontend"

# Check if production worktree exists
PRODUCTION_WORKTREE=$(git worktree list | grep production | awk '{print $1}')

if [ -n "$PRODUCTION_WORKTREE" ]; then
    print_status "Found production worktree at: $PRODUCTION_WORKTREE"
    PRODUCTION_DIR="$PRODUCTION_WORKTREE"
else
    print_status "No production worktree found. Using regular branch checkout..."

    # Check if production branch exists locally
    if git show-ref --verify --quiet refs/heads/production; then
        print_status "Production branch exists locally. Switching to it..."
        git checkout production
    else
        print_status "Production branch doesn't exist locally. Creating it..."
        # Check if production branch exists on remote
        if git ls-remote --heads origin production | grep -q production; then
            print_status "Production branch exists on remote. Checking it out..."
            git checkout -b production origin/production
        else
            print_status "Creating new production branch..."
            git checkout --orphan production
        fi
    fi
    PRODUCTION_DIR="."
fi

# Step 4: Wipe the production branch clean
print_status "Step 4: Cleaning production branch..."

if [ "$PRODUCTION_DIR" != "." ]; then
    # Working with worktree
    print_status "Cleaning production worktree directory..."
    # Remove all files except .git directory
    find "$PRODUCTION_DIR" -maxdepth 1 -not -name '.git' -not -name '.' -exec rm -rf {} + 2>/dev/null || true

    # Change to production directory and clear git index
    cd "$PRODUCTION_DIR"
    git rm -rf . 2>/dev/null || true
else
    # Working with regular checkout
    # Remove all files except .git directory
    find . -maxdepth 1 -not -name '.git' -not -name '.' -exec rm -rf {} + 2>/dev/null || true

    # Clear git index
    git rm -rf . 2>/dev/null || true
fi

print_success "Production branch cleaned!"

# Step 5: Copy contents from temporary directory into branch root
print_status "Step 5: Copying build output to production branch..."

# Copy all contents from temporary directory to current directory
if [ -d "$TEMP_OUT_DIR" ] && [ -n "$(ls -A "$TEMP_OUT_DIR")" ]; then
    print_status "Copying static files to production branch..."
    # Copy only the static files from the temporary directory
    for item in "$TEMP_OUT_DIR"/*; do
        if [ -e "$item" ]; then
            cp -r "$item" . 2>/dev/null || true
        fi
    done
    # Copy hidden files if any
    for item in "$TEMP_OUT_DIR"/.*; do
        if [ -e "$item" ] && [ "$(basename "$item")" != "." ] && [ "$(basename "$item")" != ".." ]; then
            cp -r "$item" . 2>/dev/null || true
        fi
    done
else
    print_error "Temporary out directory not found or empty at: $TEMP_OUT_DIR"
    if [ "$PRODUCTION_DIR" != "." ]; then
        cd "$MAIN_PROJECT_DIR"
    else
        git checkout main
    fi
    # Clean up temporary directory
    rm -rf "$TEMP_OUT_DIR" 2>/dev/null || true
    exit 1
fi

# Verify files were copied
if [ -z "$(ls -A .)" ]; then
    print_error "Failed to copy files from temporary directory!"
    if [ "$PRODUCTION_DIR" != "." ]; then
        cd "$MAIN_PROJECT_DIR"
    else
        git checkout main
    fi
    # Clean up temporary directory
    rm -rf "$TEMP_OUT_DIR" 2>/dev/null || true
    exit 1
fi

print_success "Build output copied successfully!"

# Step 6: Commit the changes
print_status "Step 6: Committing changes..."

# Add all files
git add .

# Check if there are changes to commit
if git diff --staged --quiet; then
    print_warning "No changes to commit. The production branch is already up to date."
else
    # Create commit with current date
    COMMIT_DATE=$(date +"%Y-%m-%d %H:%M:%S")
    COMMIT_MESSAGE="Deploy: $COMMIT_DATE"

    git commit -m "$COMMIT_MESSAGE"
    print_success "Changes committed with message: $COMMIT_MESSAGE"
fi

# Step 7: Push forcefully to remote production branch
print_status "Step 7: Pushing to remote production branch..."

if git remote | grep -q origin; then
    git push -f origin production
    print_success "Successfully pushed to remote production branch!"
else
    print_warning "No 'origin' remote found. Skipping push."
fi

# Step 8: Return to main directory and branch
print_status "Step 8: Returning to main branch..."

if [ "$PRODUCTION_DIR" != "." ]; then
    # Return to main worktree directory
    cd "$MAIN_PROJECT_DIR"
    print_status "Returned to main worktree directory"
else
    # Switch back to main branch
    git checkout main
fi

# Clean up temporary directory
print_status "Cleaning up temporary directory..."
rm -rf "$TEMP_OUT_DIR" 2>/dev/null || true

print_success "Deployment completed successfully!"
print_status "Summary:"
print_status "- Built and exported Next.js application"
print_status "- Deployed static files to production branch"
print_status "- Pushed changes to remote"
print_status "- Returned to main branch"

echo ""
print_success "🚀 Your static site has been deployed to the production branch!"
